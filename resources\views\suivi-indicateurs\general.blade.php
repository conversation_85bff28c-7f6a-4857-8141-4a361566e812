@extends('layout.app')

@section('title', 'Suivi des Indicateurs')

@section('content')
<div class="container-fluid">
    <!-- En-tête de la page -->
    <div class="row">
        <div class="col-lg-12">
            <div class="breadcome-list single-page-breadcome">
                <div class="row">
                    <div class="col-lg-6 col-md-6 col-sm-6 col-xs-12">
                        <div class="breadcome-heading">
                            <h4 style="color: #fb9678;">Suivi des Indicateurs</h4>
                        </div>
                    </div>
                    <div class="col-lg-6 col-md-6 col-sm-6 col-xs-12">
                        <ul class="breadcome-menu">
                            <li><a href="{{ route('dashboard') }}">Accueil</a> <span class="bread-slash">/</span></li>
                            <li><span class="bread-blod">Suivi des Indicateurs</span></li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistiques générales -->
    <div class="row">
        <div class="col-lg-3 col-md-6 col-sm-6 col-xs-12">
            <div class="analytics-sparkle-line reso-mg-b-30">
                <div class="analytics-content">
                    <h5>Total Indicateurs</h5>
                    <h2><span class="counter">{{ $totalIndicateurs }}</span></h2>
                    <span class="text-success">Indicateurs définis</span>
                    <div class="progress m-b-0">
                        <div class="progress-bar progress-bar-success" role="progressbar" aria-valuenow="100" aria-valuemin="0" aria-valuemax="100" style="width: 100%"></div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-lg-3 col-md-6 col-sm-6 col-xs-12">
            <div class="analytics-sparkle-line reso-mg-b-30">
                <div class="analytics-content">
                    <h5>Indicateurs Actifs</h5>
                    <h2><span class="counter">{{ $indicateursActifs }}</span></h2>
                    <span class="text-info">Avec mesures</span>
                    <div class="progress m-b-0">
                        <div class="progress-bar progress-bar-info" role="progressbar" aria-valuenow="{{ $totalIndicateurs > 0 ? ($indicateursActifs / $totalIndicateurs) * 100 : 0 }}" aria-valuemin="0" aria-valuemax="100" style="width: {{ $totalIndicateurs > 0 ? ($indicateursActifs / $totalIndicateurs) * 100 : 0 }}%"></div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-lg-3 col-md-6 col-sm-6 col-xs-12">
            <div class="analytics-sparkle-line reso-mg-b-30">
                <div class="analytics-content">
                    <h5>Total Mesures</h5>
                    <h2><span class="counter">{{ $totalEvolutions }}</span></h2>
                    <span class="text-warning">Évolutions enregistrées</span>
                    <div class="progress m-b-0">
                        <div class="progress-bar progress-bar-warning" role="progressbar" aria-valuenow="100" aria-valuemin="0" aria-valuemax="100" style="width: 100%"></div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-lg-3 col-md-6 col-sm-6 col-xs-12">
            <div class="analytics-sparkle-line reso-mg-b-30">
                <div class="analytics-content">
                    <h5>Réformes</h5>
                    <h2><span class="counter">{{ $reformes->count() }}</span></h2>
                    <span class="text-primary">Avec indicateurs</span>
                    <div class="progress m-b-0">
                        <div class="progress-bar progress-bar-primary" role="progressbar" aria-valuenow="100" aria-valuemin="0" aria-valuemax="100" style="width: 100%"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Liste des réformes avec indicateurs -->
    <div class="row">
        <div class="col-lg-8 col-md-8 col-sm-8 col-xs-12">
            <div class="sparkline13-list">
                <div class="sparkline13-hd">
                    <div class="main-sparkline13-hd">
                        <h1>Réformes et leurs Indicateurs</h1>
                    </div>
                </div>
                <div class="sparkline13-graph">
                    <div class="datatable-dashv1-list custom-datatable-overright">
                        @if($reformes->count() > 0)
                            @foreach($reformes as $reforme)
                                <div class="panel panel-default">
                                    <div class="panel-heading">
                                        <h4 class="panel-title">
                                            <a data-toggle="collapse" href="#reforme-{{ $reforme->id }}">
                                                <i class="fa fa-flag"></i> {{ $reforme->titre }}
                                                <span class="badge pull-right">{{ $reforme->indicateurs->count() }} indicateurs</span>
                                            </a>
                                        </h4>
                                    </div>
                                    <div id="reforme-{{ $reforme->id }}" class="panel-collapse collapse">
                                        <div class="panel-body">
                                            @if($reforme->indicateurs->count() > 0)
                                                <div class="table-responsive">
                                                    <table class="table table-striped">
                                                        <thead>
                                                            <tr>
                                                                <th>Indicateur</th>
                                                                <th>Unité</th>
                                                                <th>Dernière mesure</th>
                                                                <th>Actions</th>
                                                            </tr>
                                                        </thead>
                                                        <tbody>
                                                            @foreach($reforme->indicateurs as $indicateur)
                                                                @php
                                                                    $reformeIndicateur = $reforme->reformeIndicateurs->where('indicateur_id', $indicateur->id)->first();
                                                                    $derniereEvolution = $reformeIndicateur ? $reformeIndicateur->evolutions->sortByDesc('date_evolution')->first() : null;
                                                                @endphp
                                                                <tr>
                                                                    <td>{{ $indicateur->libelle }}</td>
                                                                    <td>{{ $indicateur->unite }}</td>
                                                                    <td>
                                                                        @if($derniereEvolution)
                                                                            <span class="label label-success">{{ $derniereEvolution->valeur }} {{ $indicateur->unite }}</span>
                                                                            <br><small class="text-muted">{{ $derniereEvolution->date_evolution->format('d/m/Y') }}</small>
                                                                        @else
                                                                            <span class="label label-default">Aucune mesure</span>
                                                                        @endif
                                                                    </td>
                                                                    <td>
                                                                        <a href="{{ route('suivi-indicateurs.index', $reforme->id) }}" class="btn btn-primary btn-xs">
                                                                            <i class="fa fa-line-chart"></i> Voir le suivi
                                                                        </a>
                                                                    </td>
                                                                </tr>
                                                            @endforeach
                                                        </tbody>
                                                    </table>
                                                </div>
                                            @else
                                                <p class="text-muted">Aucun indicateur associé à cette réforme.</p>
                                            @endif
                                        </div>
                                    </div>
                                </div>
                            @endforeach
                        @else
                            <div class="alert alert-info">
                                <i class="fa fa-info-circle"></i> Aucune réforme avec indicateurs trouvée.
                            </div>
                        @endif
                    </div>
                </div>
            </div>
        </div>

        <!-- Évolutions récentes -->
        <div class="col-lg-4 col-md-4 col-sm-4 col-xs-12">
            <div class="sparkline13-list">
                <div class="sparkline13-hd">
                    <div class="main-sparkline13-hd">
                        <h1>Évolutions Récentes</h1>
                    </div>
                </div>
                <div class="sparkline13-graph">
                    <div class="datatable-dashv1-list custom-datatable-overright">
                        @if($evolutionsRecentes->count() > 0)
                            @foreach($evolutionsRecentes as $evolution)
                                <div class="alert alert-info alert-mg-b-10">
                                    <div class="alert-title">
                                        <strong>{{ $evolution->reformeIndicateur->indicateur->libelle }}</strong>
                                    </div>
                                    <div class="alert-text">
                                        <small class="text-muted">{{ $evolution->reformeIndicateur->reforme->titre }}</small><br>
                                        <span class="label label-primary">{{ $evolution->valeur }} {{ $evolution->reformeIndicateur->indicateur->unite }}</span>
                                        <small class="pull-right text-muted">{{ $evolution->date_evolution->format('d/m/Y') }}</small>
                                    </div>
                                </div>
                            @endforeach
                        @else
                            <div class="alert alert-warning">
                                <i class="fa fa-exclamation-triangle"></i> Aucune évolution récente.
                            </div>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script>
$(document).ready(function() {
    // Animation des compteurs
    $('.counter').each(function() {
        var $this = $(this);
        var countTo = $this.text();
        
        $({ countNum: 0 }).animate({
            countNum: countTo
        }, {
            duration: 2000,
            easing: 'linear',
            step: function() {
                $this.text(Math.floor(this.countNum));
            },
            complete: function() {
                $this.text(this.countNum);
            }
        });
    });
});
</script>
@endsection
