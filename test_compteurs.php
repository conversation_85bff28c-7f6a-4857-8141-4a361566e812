<?php

require_once 'vendor/autoload.php';

use App\Models\Indicateur;
use App\Models\EvolutionIndicateur;
use App\Models\ReformeIndicateur;
use App\Models\Reforme;

// Initialiser Laravel
$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "Test des requêtes du contrôleur:\n";
echo "=================================\n";

try {
    $totalIndicateurs = Indicateur::count();
    echo "totalIndicateurs: $totalIndicateurs\n";
} catch (Exception $e) {
    echo "Erreur totalIndicateurs: " . $e->getMessage() . "\n";
}

try {
    $totalEvolutions = EvolutionIndicateur::count();
    echo "totalEvolutions: $totalEvolutions\n";
} catch (Exception $e) {
    echo "Erreur totalEvolutions: " . $e->getMessage() . "\n";
}

try {
    $indicateursActifs = ReformeIndicateur::distinct('indicateur_id')->count();
    echo "indicateursActifs: $indicateursActifs\n";
} catch (Exception $e) {
    echo "Erreur indicateursActifs: " . $e->getMessage() . "\n";
}

try {
    $reformes = Reforme::with(['indicateurs', 'reformeIndicateurs.evolutions'])->orderBy('titre')->get();
    echo "reformes count: " . $reformes->count() . "\n";
} catch (Exception $e) {
    echo "Erreur reformes: " . $e->getMessage() . "\n";
}

echo "\nDétails des données:\n";
echo "===================\n";

try {
    $indicateurs = Indicateur::all();
    echo "Indicateurs trouvés:\n";
    foreach ($indicateurs as $indicateur) {
        echo "- {$indicateur->libelle} ({$indicateur->unite})\n";
    }
} catch (Exception $e) {
    echo "Erreur lors de la récupération des indicateurs: " . $e->getMessage() . "\n";
}

try {
    $reformes = Reforme::all();
    echo "\nRéformes trouvées:\n";
    foreach ($reformes as $reforme) {
        echo "- {$reforme->titre}\n";
    }
} catch (Exception $e) {
    echo "Erreur lors de la récupération des réformes: " . $e->getMessage() . "\n";
}

try {
    $associations = ReformeIndicateur::all();
    echo "\nAssociations reforme-indicateur:\n";
    foreach ($associations as $assoc) {
        echo "- Réforme ID: {$assoc->reforme_id}, Indicateur ID: {$assoc->indicateur_id}\n";
    }
} catch (Exception $e) {
    echo "Erreur lors de la récupération des associations: " . $e->getMessage() . "\n";
}

try {
    $evolutions = EvolutionIndicateur::all();
    echo "\nÉvolutions trouvées: " . $evolutions->count() . "\n";
    foreach ($evolutions->take(5) as $evolution) {
        echo "- Reforme-Indicateur ID: {$evolution->reforme_indicateur_id}, Date: {$evolution->date_evolution}, Valeur: {$evolution->valeur}\n";
    }
} catch (Exception $e) {
    echo "Erreur lors de la récupération des évolutions: " . $e->getMessage() . "\n";
}
