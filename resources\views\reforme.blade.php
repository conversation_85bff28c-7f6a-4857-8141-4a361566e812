@extends('layout.app')

@section('content')


<div class="container pt-4">
    @if(session('success'))
    <div class="alert alert-success">{{ session('success') }}</div>
    @endif

    <!-- Mobile Menu end -->
 <div class="breadcome-area">
            <div class="container-fluid">
                <div class="row">
                    <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
                        <div class="breadcome-list">
                            <div class="row">
                                <div class="col-lg-6 col-md-6 col-sm-6 col-xs-12">
                                    <div class="breadcome-heading">
                                        <form role="search" class="sr-input-func">
                                            <input type="text" placeholder="Search..." class="search-int form-control">
                                            <a href="#"><i class="fa fa-search"></i></a>
                                        </form>
                                    </div>
                                </div>
                                <div class="col-lg-6 col-md-6 col-sm-6 col-xs-12">
                                    <ul class="breadcome-menu">
                                        <li><span class="bread-blod">Gestion des réformes</span>
                                        </li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div> 

    <!-- Bouton Ajouter -->
    

    <!-- Tableau des réformes -->
    <div class="data-table-area" style="margin-bottom: 30px;">
        <div class="container-fluid">
            <div class="row">
                <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
                    <div class="sparkline13-list" style="background: white; border-radius: 8px; box-shadow: 0 4px 15px rgba(0,0,0,0.1); margin-bottom: 30px;">
                        <div class="sparkline13-hd" style="padding: 25px 30px; border-bottom: 1px solid #eee; background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%); border-radius: 8px 8px 0 0;">
                            <div class="main-sparkline13-hd" style="display: flex; justify-content: space-between; align-items: center;">
                                <h1 style="margin: 0; color: #333; font-size: 1.8em; font-weight: 600;">
                                    <i class="fa fa-flag" style="color: #e74c3c; margin-right: 12px;"></i>
                                    Liste <span class="table-project-n" style="color: #3498db;">des </span>Réformes
                                </h1>
                                @canCreateUrl('/reforme')
                                <button type="button" class="btn btn-primary" data-toggle="modal" data-target="#addModal"
                                        style="padding: 12px 24px; border-radius: 6px; font-weight: 600; box-shadow: 0 2px 8px rgba(52, 152, 219, 0.3); border: none; background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);">
                                    <i class="fa fa-plus" style="margin-right: 8px;"></i> Ajouter une réforme
                                </button>
                                @endcanCreateUrl
                            </div>
                        </div>
                        <div class="sparkline13-graph" style="padding: 30px;">
                            <div class="datatable-dashv1-list custom-datatable-overright">
                                <div id="toolbar" style="margin-bottom: 20px; padding: 15px; background: #f8f9fa; border-radius: 6px; border: 1px solid #dee2e6;">
                                    <select class="form-control dt-tb" style="max-width: 200px; border-radius: 4px;">
                                        <option value="">Export Basic</option>
                                        <option value="all">Export All</option>
                                        <option value="selected">Export Selected</option>
                                    </select>
                                </div>
                                <table id="table" data-toggle="table" data-pagination="true" data-search="true" data-show-columns="true" data-show-pagination-switch="true" data-show-refresh="true" data-key-events="true" data-show-toggle="true" data-resizable="true" data-cookie="true"
                                    data-cookie-id-table="saveId" data-show-export="true" data-click-to-select="true" data-toolbar="#toolbar">
                                    <thead>
                                        <tr>
                                            <th data-field="state" data-checkbox="true"></th>
                                            <th data-field="id">ID</th>
                                            <th data-field="titre" data-editable="true">titre</th>
                                            <th data-field="objectifs" data-editable="true">objectifs</th>
                                            <th data-field="budget" data-editable="true">budget</th>
                                            <th data-field="date_debut" data-editable="true">date_debut</th>
                                            <th data-field="date_fin_prevue" data-editable="true">date fin prevue</th>
                                            <th data-field="date_fin" data-editable="true">date fin</th>
                                            <th data-field="statut" data-formatter="statutFormatter">Statut</th>
                                            <th data-field="typereforme" data-editable="true">type reforme</th>
                                            <th data-field="action">Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach($reformes as $reforme)
                                        <tr>
                                            <td></td>
                                            <td>{{ $reforme->id }}</td>
                                            <td>{{ $reforme->titre }}</td>
                                            <td>{{ $reforme->objectifs }}</td>
                                            <td>{{ $reforme->budget }}</td>
                                            <td>{{ $reforme->date_debut }}</td>
                                            <td>{{ $reforme->date_fin_prevue }}</td>
                                            <td>{{ $reforme->date_fin }}</td>
                                            <td>
                                                {!! $reforme->status_badge !!}
                                            </td>
                                            <td>{{ $reforme->type->lib ?? 'Non défini' }}</td>
                                             <td class="text-center">
                                                <div class="btn-group btn-group-sm d-flex justify-content-center" role="group" aria-label="Actions" style="gap: 5px;">
                                                    <!-- Voir -->
                                                    <button class="btn btn-info btn-sm" data-toggle="modal" data-target="#viewModal{{ $reforme->id }}">
                                                        <i class="fa fa-eye"></i>
                                                    </button>
                                                    <!-- Modifier -->
                                                    <button class="btn btn-warning btn-sm" data-toggle="modal" data-target="#editModal{{ $reforme->id }}">
                                                        <i class="fa fa-edit"></i>
                                                    </button>
                                                    <!-- Supprimer -->
                                                    <form action="{{ route('reforme.destroy', $reforme->id) }}" method="POST" style="display:inline-block; margin:0;">
                                                        @csrf @method('DELETE')
                                                        <button class="btn btn-danger btn-sm" onclick="return confirm('Supprimer cette réforme ?')">
                                                            <i class="fa fa-trash"></i>
                                                        </button>
                                                    </form>
                                                </div>
                                            </td>
                                        </tr>
                                        
                                        <!-- Modal de visualisation -->
                                        <div class="modal fade" id="viewModal{{ $reforme->id }}" tabindex="-1" role="dialog" aria-labelledby="viewModal{{ $reforme->id }}" aria-hidden="true">
                                            <div class="modal-dialog modal-dialog modal-lg" role="document">
                                                <div class="modal-content">
                                                <div class="modal-header">
                                                        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                                            <span aria-hidden="true">&times;</span>
                                                        </button>
                                                        <ul id="myTabedu1" class="tab-review-design">
                                                            <li class="active"><a href="#description">Détail de la reforme</a></li>
                                                        </ul>
                                                    </div>
                                                    <div class="modal-body">
                                                        <p><strong>ID:</strong> {{ $reforme->id }}</p>
                                                        <p><strong>Titre:</strong> {{ $reforme->titre }}</p>
                                                        <p><strong>Objectifs:</strong> {{ $reforme->objectifs }}</p>
                                                        <p><strong>Budget:</strong> {{ $reforme->budget }}</p>
                                                        <p><strong>Date début:</strong> {{ $reforme->date_debut }}</p>
                                                        <p><strong>Date fin prevue:</strong> {{ $reforme->date_fin_prevue }}</p>
                                                        <p><strong>Date fin:</strong> {{ $reforme->date_fin }}</p>
                                                        <p><strong>Statut:</strong>
                                                            <span class="badge badge-{{ $reforme->statut == 'Terminé' ? 'success' : ($reforme->statut == 'En cours' ? 'warning' : 'secondary') }}">
                                                                {{ $reforme->statut }}
                                                            </span>
                                                        </p>
                                                        <p><strong>Type:</strong> {{ $reforme->type->lib ?? 'Non défini' }}</p>
                                                        <p><strong>Pièces justificatives:</strong> {{ $reforme->pieces_justificatifs ?? 'Aucune' }}</p>
                                                    </div>
                                                    <div class="modal-footer">
                                                        <a href="{{ route('suivi-indicateurs.index', $reforme->id) }}"
                                                           class="btn btn-info pull-left">
                                                            <i class="fa fa-line-chart"></i> Suivi des Indicateurs
                                                        </a>
                                                        <button class="btn btn-default" data-dismiss="modal">Fermer</button>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- Modal de modification -->
                                        <div class="modal fade" id="editModal{{ $reforme->id }}" tabindex="-1" role="dialog">
                                            <div class="modal-dialog modal-dialog modal-lg" role="document">
                                                <form action="{{ route('reforme.update', $reforme->id) }}" method="POST">
                                                    @csrf @method('PUT')
                                                    <div class="modal-content">
                                                    <div class="modal-header">
                                                        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                                            <span aria-hidden="true">&times;</span>
                                                        </button>
                                                        <ul id="myTabedu1" class="tab-review-design">
                                                            <li class="active"><a href="#description">Modifier reforme</a></li>
                                                        </ul>
                                                    </div>
                                                        <div class="modal-body">
                                                        <div class="col-lg-6 col-md-6 col-sm-6 col-xs-12">
                                                            <div class="mb-3">
                                                                <label>Titre</label>
                                                                <input type="text" class="form-control" name="titre" value="{{ $reforme->titre }}" required>
                                                            </div>
                                                            <div class="mb-3">
                                                                <label>Objectifs</label>
                                                                <textarea class="form-control" name="objectifs">{{ $reforme->objectifs }}</textarea>
                                                            </div>
                                                            <div class="mb-3">
                                                                <label>Budget</label>
                                                                <input type="number" class="form-control" name="budget" value="{{ $reforme->budget }}">
                                                            </div>
                                                            <div class="mb-3">
                                                                <label>Date Début</label>
                                                                <input type="date" class="form-control" name="date_debut" value="{{ $reforme->date_debut }}">
                                                            </div>
                                                        </div>
                                                        <div class="col-lg-6 col-md-6 col-sm-6 col-xs-12">
                                                            <div class="mb-3">
                                                                <label>Date de Fin Prevue</label>
                                                                <input type="date" name="date_fin_prevue" class="form-control" value="{{ $reforme->date_fin_prevue }}">
                                                            </div>
                                                            <div class="mb-3">
                                                                <label>Date Fin</label>
                                                                <input type="date" class="form-control" name="date_fin" value="{{ $reforme->date_fin }}">
                                                            </div>
                                                            <div class="mb-3">
                                                                <label>Statut Manuel</label>
                                                                <select name="statut_manuel" class="form-control">
                                                                    <option value="">Automatique (basé sur les dates)</option>
                                                                    <option value="En cours" {{ $reforme->statut_manuel == 'En cours' ? 'selected' : '' }}>En cours</option>
                                                                    <option value="En pause" {{ $reforme->statut_manuel == 'En pause' ? 'selected' : '' }}>En pause</option>
                                                                    <option value="Achevé" {{ $reforme->statut_manuel == 'Achevé' ? 'selected' : '' }}>Achevé</option>
                                                                </select>
                                                                <small class="text-muted">Laissez sur "Automatique" pour que le statut soit calculé selon les dates</small>
                                                            </div>
                                                            <div class="mb-3">
                                                                <label>Pièce Justificatif</label>
                                                                <input type="text" name="pieces_justificatifs" class="form-control" value="{{ $reforme->pieces_justificatifs }}">
                                                            </div>
                                                            <div class="mb-3">
                                                                <label>Type de réforme</label>
                                                                <select name="type_reforme" class="form-control">
                                                                
                                                                    @foreach($typereformes ?? [] as $type)
                                                                    <option value="{{ $type->id }}" {{ $reforme->type_reforme == $type->id ? 'selected' : '' }}>
                                                                        {{ $type->lib }}
                                                                    </option>
                                                                    @endforeach
                                                                </select>
                                                            </div>
                                                        </div>
                                                        </div>
                                                        <div class="modal-footer">
                                                            <button class="btn btn-primary" type="submit">Mettre à jour</button>
                                                            <button class="btn btn-default" type="button" data-dismiss="modal">Annuler</button>
                                                        </div>
                                                    </div>
                                                </form>
                                            </div>
                                        </div>
                                        @endforeach
                                    </tbody>
                                </table>

                                
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

</div>




<!-- Modal d'ajout -->
<div class="modal fade" id="addModal" tabindex="-1" role="dialog" aria-labelledby="addModalLabel">
    <div class="modal-dialog modal-lg" role="document">
        <form action="{{ route('reforme.store') }}" method="POST" id="add-department" class="add-department">
            @csrf
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                    <ul id="myTabedu1" class="tab-review-design">
                        <li class="active"><a href="#description">Ajouter reforme</a></li>
                    </ul>
                </div>
                <div class="modal-body">
                <div class="row">
                    <div class="col-lg-6 col-md-6 col-sm-6 col-xs-12">
                        <div class="mb-3">
                            <label>Titre</label>
                            <input type="text" name="titre" class="form-control" required>
                        </div>
                        <div class="mb-3">
                            <label>Objectifs</label>
                            <input type="text" name="objectifs" class="form-control" required>
                        </div>
                        <div class="mb-3">
                            <label>Budget</label>
                            <input type="number" name="budget" class="form-control">
                        </div>
                        <div class="mb-3">
                            <label>Date Début</label>
                            <input type="date" name="date_debut" class="form-control">
                        </div>
                        <div class="mb-3">
                            <label>Structures concernées</label>
                            <select name="structures[]" id="structures-select" class="form-control select2-multiple" multiple>
                                @foreach($structures as $structure)
                                    <option value="{{ $structure->id }}">{{ $structure->lib_long ?? $structure->lib_court ?? $structure->id }}</option>
                                @endforeach
                            </select>
                            <small class="text-muted">Sélectionnez une ou plusieurs structures</small>
                        </div>
                    </div>
                    <div class="col-lg-6 col-md-6 col-sm-6 col-xs-12">
                        <div class="mb-3">
                            <label>Date de Fin Prevue</label>
                            <input type="date" name="date_fin_prevue" class="form-control">
                        </div>
                        <div class="mb-3">
                            <label>Date de Fin Réelle</label>
                            <input type="date" name="date_fin" class="form-control">
                            <small class="text-muted">Laissez vide si la réforme n'est pas encore terminée</small>
                        </div>
                        <div class="mb-3">
                            <label>Pièce Justificatif</label>
                            <input type="text" name="pieces_justificatifs" class="form-control">
                        </div>
                        <div class="mb-3">
                            <label>Type de réforme</label>
                            <select name="type_reforme" class="form-control">
                                @foreach($typereformes as $type)
                                <option value="{{ $type->id }}">{{ $type->lib }}</option>
                                @endforeach
                            </select>
                        </div>
                        <div class="mb-3">
                            <label>Indicateurs associés</label>
                            <select name="indicateurs[]" id="indicateurs-select" class="form-control select2-multiple" multiple>
                                @foreach($indicateurs as $indicateur)
                                    <option value="{{ $indicateur->id }}">{{ $indicateur->libelle }}</option>
                                @endforeach
                            </select>
                            <small class="text-muted">Sélectionnez un ou plusieurs indicateurs de suivi</small>
                        </div>
                    </div>
                </div>
                <div class="modal-footer ">
                    <button class="btn btn-primary" type="submit">Enregistrer</button>
                    <button class="btn btn-default" type="button" data-dismiss="modal">Annuler</button>
                </div>
            </div>
            </div>
        </form>
    </div>
</div>



@endsection

@section('scripts')
<script>
function statutFormatter(value, row, index) {
    return value;
}
$(document).ready(function() {
    // Initialiser Select2 pour les sélections multiples
    $('.select2-multiple').select2({
        placeholder: "Cliquez pour sélectionner...",
        allowClear: true,
        width: '100%',
        language: {
            noResults: function() {
                return "Aucun résultat trouvé";
            },
            searching: function() {
                return "Recherche en cours...";
            },
            removeAllItems: function() {
                return "Supprimer tous les éléments";
            }
        }
    });

    // Configuration spécifique pour les structures
    $('#structures-select').select2({
        placeholder: "Sélectionnez les structures concernées...",
        allowClear: true,
        width: '100%',
        language: {
            noResults: function() {
                return "Aucune structure trouvée";
            },
            searching: function() {
                return "Recherche de structures...";
            }
        }
    });

    // Configuration spécifique pour les indicateurs
    $('#indicateurs-select').select2({
        placeholder: "Sélectionnez les indicateurs de suivi...",
        allowClear: true,
        width: '100%',
        language: {
            noResults: function() {
                return "Aucun indicateur trouvé";
            },
            searching: function() {
                return "Recherche d'indicateurs...";
            }
        }
    });

    // Réinitialiser Select2 quand le modal se ferme
    $('#addModal').on('hidden.bs.modal', function () {
        $('.select2-multiple').val(null).trigger('change');
    });
});
</script>
@endsection

@section('styles')
<style>
.btn-group.d-flex {
    display: flex !important;
    flex-direction: row;
    align-items: center;
    justify-content: center;
    gap: 5px;
}
.btn-group .btn { margin-right: 0 !important; }
</style>
@endsection
