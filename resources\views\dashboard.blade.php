@extends('layout.app')

@section('content')
<style>
/* Styles personnalisés pour le dashboard amélioré */
.dashboard-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 30px 0;
    margin-bottom: 30px;
    border-radius: 8px;
}

.dashboard-title {
    font-size: 2.5em;
    font-weight: 300;
    margin-bottom: 10px;
}

.dashboard-subtitle {
    font-size: 1.1em;
    opacity: 0.9;
}

.stat-card {
    background: white;
    border-radius: 12px;
    padding: 25px;
    margin-bottom: 20px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    border-left: 4px solid #ddd;
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.stat-card.users { border-left-color: #3498db; }
.stat-card.activities { border-left-color: #e74c3c; }
.stat-card.reforms { border-left-color: #2ecc71; }
.stat-card.indicators { border-left-color: #f39c12; }

.stat-number {
    font-size: 2.8em;
    font-weight: 700;
    margin-bottom: 5px;
    line-height: 1;
}

.stat-label {
    font-size: 1.1em;
    color: #666;
    margin-bottom: 15px;
}

.stat-progress {
    height: 8px;
    background: #f0f0f0;
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: 10px;
}

.stat-progress-bar {
    height: 100%;
    border-radius: 4px;
    transition: width 0.8s ease;
}

.stat-percentage {
    font-size: 1.2em;
    font-weight: 600;
}

.chart-container {
    background: white;
    border-radius: 12px;
    padding: 25px;
    margin-bottom: 20px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.chart-title {
    font-size: 1.4em;
    font-weight: 600;
    margin-bottom: 20px;
    color: #333;
}

.activity-item {
    padding: 15px;
    border-bottom: 1px solid #eee;
    transition: background 0.2s ease;
}

.activity-item:hover {
    background: #f8f9fa;
}

.activity-item:last-child {
    border-bottom: none;
}

.activity-date {
    font-size: 0.9em;
    color: #666;
}

.activity-title {
    font-weight: 600;
    color: #333;
    margin-bottom: 5px;
}

.activity-reform {
    font-size: 0.9em;
    color: #888;
}

.refresh-btn {
    position: fixed;
    bottom: 30px;
    right: 30px;
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: #667eea;
    color: white;
    border: none;
    font-size: 1.5em;
    box-shadow: 0 4px 15px rgba(0,0,0,0.2);
    transition: all 0.3s ease;
    z-index: 1000;
}

.refresh-btn:hover {
    background: #5a67d8;
    transform: scale(1.1);
}

.refresh-btn:active {
    transform: scale(0.95);
}

@media (max-width: 768px) {
    .dashboard-title {
        font-size: 2em;
    }

    .stat-number {
        font-size: 2.2em;
    }

    .stat-card {
        padding: 20px;
    }

    .refresh-btn {
        bottom: 20px;
        right: 20px;
        width: 50px;
        height: 50px;
        font-size: 1.2em;
    }
}

.status-indicator {
    display: inline-block;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    margin-right: 8px;
}

.status-online { background-color: #2ecc71; }
.status-busy { background-color: #f39c12; }
.status-offline { background-color: #95a5a6; }
</style>

<div class="container-fluid">
    <!-- Header du Dashboard -->
    <div class="dashboard-header">
        <div class="container-fluid">
            <div class="row">
                <div class="col-lg-8 col-md-8 col-sm-12 col-xs-12">
                    <h1 class="dashboard-title">
                        <i class="fa fa-tachometer"></i> Tableau de Bord
                    </h1>
                    <p class="dashboard-subtitle">
                        Vue d'ensemble des activités et réformes - Mise à jour en temps réel
                    </p>
                </div>
                <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 text-right">
                    <div style="margin-top: 20px;">
                        <span class="status-indicator status-online"></span>
                        <span style="color: rgba(255,255,255,0.9);">
                            {{ $utilisateursConnectes }} utilisateur(s) connecté(s)
                        </span>
                    </div>
                    <div style="margin-top: 10px; font-size: 0.9em; opacity: 0.8;">
                        Dernière mise à jour : <span id="last-update">{{ now()->format('H:i:s') }}</span>
                    </div>
                </div>
            </div>
        </div>
    </div>



    <!-- Statistiques Principales -->
    <div class="row">
        <!-- Utilisateurs Connectés -->
        <div class="col-lg-3 col-md-6 col-sm-6 col-xs-12">
            <div class="stat-card users">
                <div class="stat-number" style="color: #3498db;">
                    {{ $utilisateursConnectes }}
                </div>
                <div class="stat-label">
                    <i class="fa fa-users"></i> Utilisateurs Connectés
                </div>
                
            </div>
        </div>

        <!-- Activités -->
        <div class="col-lg-3 col-md-6">
            <div class="stat-card activities">
                <div class="stat-number" style="color: #e74c3c;">
                    {{ $totalActivites ?? 0 }}
                </div>
                <div class="stat-label">
                    <i class="fa fa-check-circle"></i> Activités 
                </div>
               
            </div>
        </div>

        <!-- Réformes Validées -->
        <div class="col-lg-3 col-md-6 col-sm-6 col-xs-12">
            <div class="stat-card reforms">
                <div class="stat-number" style="color: #2ecc71;">
                   {{ $totalReformes }}
                </div>
                <div class="stat-label">
                    <i class="fa fa-flag-checkered"></i> Réformes 
                </div>
               
            </div>
        </div>

        <!-- Indicateurs -->
        <div class="col-lg-3 col-md-6 col-sm-6 col-xs-12">
            <div class="stat-card indicators">
                <div class="stat-number" style="color: #f39c12;">
                    {{ $totalIndicateurs }}
                </div>
                <div class="stat-label">
                    <i class="fa fa-line-chart"></i> Indicateurs Suivis
                </div>
                
            </div>
        </div>
    </div>


    <!-- Graphiques et Analyses -->
    <div class="row">
        <!-- Répartition des Statuts -->
        <div class="col-lg-6 col-md-12 col-sm-12 col-xs-12">
            <div class="chart-container">
                <h3 class="chart-title">
                    <i class="fa fa-pie-chart"></i> Répartition des Statuts
                </h3>
                <div class="row">
                    <div class="col-md-6">
                        <h4 style="color: #666; margin-bottom: 15px;">Activités</h4>
                        <div style="margin-bottom: 10px;">
                            <span class="label label-success">Achevées</span>
                            <span class="pull-right">{{ $activitesValidees }}</span>
                        </div>
                        <div style="margin-bottom: 10px;">
                            <span class="label label-warning">En cours</span>
                            <span class="pull-right">{{ $activitesEnCours ?? 0 }}</span>
                        </div>
                        <div style="margin-bottom: 10px;">
                            <span class="label label-danger">En pause</span>
                            <span class="pull-right">{{ $activitesEnPause ?? 0 }}</span>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <h4 style="color: #666; margin-bottom: 15px;">Réformes</h4>
                        <div style="margin-bottom: 10px;">
                            <span class="label label-success">Achevées</span>
                            <span class="pull-right">{{ $reformesValidees }}</span>
                        </div>
                        <div style="margin-bottom: 10px;">
                            <span class="label label-warning">En cours</span>
                            <span class="pull-right">{{ $reformesEnCours ?? 0 }}</span>
                        </div>
                        <div style="margin-bottom: 10px;">
                            <span class="label label-danger">En pause</span>
                            <span class="pull-right">{{ $reformesEnPause ?? 0 }}</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Réformes par Type -->
        <div class="col-lg-6 col-md-12 col-sm-12 col-xs-12">
            <div class="chart-container">
                <h3 class="chart-title">
                    <i class="fa fa-bar-chart"></i> Réformes par Type
                </h3>
                @if($reformesParType && count($reformesParType) > 0)
                    @foreach($reformesParType as $type)
                    <div style="margin-bottom: 15px;">
                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 5px;">
                            <span style="font-weight: 600;">{{ $type->lib }}</span>
                            <span class="label label-info">{{ $type->total }}</span>
                        </div>
                        <div class="progress" style="height: 8px;">
                            <div class="progress-bar progress-bar-info"
                                 style="width: {{ $totalReformes > 0 ? ($type->total / $totalReformes) * 100 : 0 }}%;">
                            </div>
                        </div>
                    </div>
                    @endforeach
                @else
                    <p class="text-muted">Aucune donnée disponible</p>
                @endif
            </div>
        </div>
    </div>

    <!-- Activités Récentes -->
    <div class="row">
        <div class="col-lg-12">
            <div class="chart-container">
                <h3 class="chart-title">
                    <i class="fa fa-clock-o"></i> Activités Récentes
                    <small class="pull-right text-muted">Dernières activités créées</small>
                </h3>
                @if($activitesRecentes && count($activitesRecentes) > 0)
                    @foreach($activitesRecentes as $activite)
                    <div class="activity-item">
                        <div class="row">
                            <div class="col-md-8 col-sm-8 col-xs-12">
                                <div class="activity-title">
                                    {{ $activite->libelle ?? 'Activité sans titre' }}
                                </div>
                                <div class="activity-reform">
                                    <i class="fa fa-folder-o"></i>
                                    {{ $activite->reforme->titre ?? 'Réforme non définie' }}
                                </div>
                            </div>
                            <div class="col-md-2 col-sm-2 col-xs-6">
                                <span class="label {{ $activite->statut == 'A' ? 'label-success' : ($activite->statut == 'P' ? 'label-warning' : 'label-default') }}">
                                    {{ $activite->statut_label }}
                                </span>
                            </div>
                            <div class="col-md-2 col-sm-2 col-xs-6">
                                <div class="activity-date">
                                    {{ $activite->created_at->format('d/m/Y') }}
                                </div>
                                <div class="activity-date">
                                    {{ $activite->created_at->format('H:i') }}
                                </div>
                            </div>
                        </div>
                    </div>
                    @endforeach
                @else
                    <p class="text-muted">Aucune activité récente</p>
                @endif
            </div>
        </div>
    </div>

    <!-- Bouton de Rafraîchissement -->
    <button class="refresh-btn" onclick="refreshDashboard()" title="Actualiser les données">
        <i class="fa fa-refresh" id="refresh-icon"></i>
    </button>
</div>

<script>
// Script pour le rafraîchissement automatique et manuel
let refreshInterval;

function refreshDashboard() {
    const icon = document.getElementById('refresh-icon');
    icon.classList.add('fa-spin');

    fetch('{{ route("dashboard") }}', {
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.text())
    .then(html => {
        // Mettre à jour l'heure de dernière mise à jour
        document.getElementById('last-update').textContent = new Date().toLocaleTimeString();

        // Animation de succès
        setTimeout(() => {
            icon.classList.remove('fa-spin');
        }, 1000);
    })
    .catch(error => {
        console.error('Erreur lors du rafraîchissement:', error);
        icon.classList.remove('fa-spin');
    });
}

// Rafraîchissement automatique toutes les 5 minutes
refreshInterval = setInterval(refreshDashboard, 300000);

// Animation des compteurs au chargement
document.addEventListener('DOMContentLoaded', function() {
    const counters = document.querySelectorAll('.stat-number');
    counters.forEach(counter => {
        const target = parseInt(counter.textContent.replace(/[^\d]/g, ''));
        let current = 0;
        const increment = target / 50;

        const timer = setInterval(() => {
            current += increment;
            if (current >= target) {
                counter.textContent = counter.textContent.replace(/\d+/, target);
                clearInterval(timer);
            } else {
                counter.textContent = counter.textContent.replace(/\d+/, Math.floor(current));
            }
        }, 30);
    });
});

// Nettoyage à la fermeture de la page
window.addEventListener('beforeunload', function() {
    if (refreshInterval) {
        clearInterval(refreshInterval);
    }
});
</script>

@endsection