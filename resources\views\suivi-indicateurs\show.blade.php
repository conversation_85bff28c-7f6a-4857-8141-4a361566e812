@extends('layout.app')

@section('title', 'Détails Indicateur - ' . $indicateur->libelle)

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
            <div class="breadcome-list single-page-breadcome">
                <div class="row">
                    <div class="col-lg-6 col-md-6 col-sm-6 col-xs-12">
                        <div class="breadcome-heading">
                            <h4>{{ $indicateur->libelle }}</h4>
                        </div>
                    </div>
                    <div class="col-lg-6 col-md-6 col-sm-6 col-xs-12">
                        <ul class="breadcome-menu">
                            <li><a href="{{ route('dashboard') }}">Accueil</a> <span class="bread-slash">/</span></li>
                            <li><a href="{{ route('reforme.index') }}">Réformes</a> <span class="bread-slash">/</span></li>
                            <li><a href="{{ route('suivi-indicateurs.index', $reforme->id) }}">Suivi Indicateurs</a> <span class="bread-slash">/</span></li>
                            <li><span class="bread-blod">{{ Str::limit($indicateur->libelle, 30) }}</span></li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Messages de feedback -->
    @if(session('success'))
        <div class="alert alert-success alert-dismissible">
            <button type="button" class="close" data-dismiss="alert">&times;</button>
            <i class="fa fa-check"></i> {{ session('success') }}
        </div>
    @endif

    @if(session('error'))
        <div class="alert alert-danger alert-dismissible">
            <button type="button" class="close" data-dismiss="alert">&times;</button>
            <i class="fa fa-exclamation-triangle"></i> {{ session('error') }}
        </div>
    @endif

    <!-- Informations générales -->
    <div class="row">
        <div class="col-lg-12">
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h3 class="panel-title">
                        <i class="fa fa-info-circle"></i> Informations Générales
                    </h3>
                    <div class="panel-actions">
                        <a href="{{ route('suivi-indicateurs.create', [$reforme->id, $indicateur->id]) }}" 
                           class="btn btn-success btn-xs">
                            <i class="fa fa-plus"></i> Ajouter une mesure
                        </a>
                        <a href="{{ route('suivi-indicateurs.index', $reforme->id) }}" 
                           class="btn btn-default btn-xs">
                            <i class="fa fa-arrow-left"></i> Retour
                        </a>
                    </div>
                </div>
                <div class="panel-body">
                    <div class="row">
                        <div class="col-md-6">
                            <p><strong>Réforme :</strong> {{ $reforme->titre }}</p>
                            <p><strong>Indicateur :</strong> {{ $indicateur->libelle }}</p>
                            <p><strong>Unité :</strong> <span class="label label-default">{{ $indicateur->unite }}</span></p>
                        </div>
                        <div class="col-md-6">
                            @if($statistiques)
                                <p><strong>Nombre de mesures :</strong> <span class="badge">{{ $statistiques['nombre_mesures'] }}</span></p>
                                <p><strong>Période :</strong> {{ \Carbon\Carbon::parse($statistiques['periode_debut'])->format('d/m/Y') }} - {{ \Carbon\Carbon::parse($statistiques['periode_fin'])->format('d/m/Y') }}</p>
                                <p><strong>Tendance :</strong> {!! $reformeIndicateur->icone_tendance_generale !!} {{ $statistiques['tendance'] }}</p>
                            @else
                                <div class="alert alert-warning">
                                    <i class="fa fa-exclamation-triangle"></i> Aucune donnée disponible
                                </div>
                            @endif
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    @if($statistiques)
        <!-- Statistiques -->
        <div class="row">
            <div class="col-md-3">
                <div class="panel panel-primary">
                    <div class="panel-body text-center">
                        <h4>Valeur Actuelle</h4>
                        <div class="metric-value text-primary">
                            {{ number_format($statistiques['valeur_actuelle'], 2, ',', ' ') }}
                        </div>
                        <small class="text-muted">{{ $indicateur->unite }}</small>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="panel panel-info">
                    <div class="panel-body text-center">
                        <h4>Progression</h4>
                        <div class="metric-value {{ $reformeIndicateur->progression >= 0 ? 'text-success' : 'text-danger' }}">
                            {{ $reformeIndicateur->progression > 0 ? '+' : '' }}{{ $reformeIndicateur->progression }}%
                        </div>
                        <small class="text-muted">depuis le début</small>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="panel panel-success">
                    <div class="panel-body text-center">
                        <h4>Valeur Max</h4>
                        <div class="metric-value text-success">
                            {{ number_format($statistiques['valeur_max'], 2, ',', ' ') }}
                        </div>
                        <small class="text-muted">{{ $indicateur->unite }}</small>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="panel panel-warning">
                    <div class="panel-body text-center">
                        <h4>Moyenne</h4>
                        <div class="metric-value text-warning">
                            {{ number_format($statistiques['moyenne'], 2, ',', ' ') }}
                        </div>
                        <small class="text-muted">{{ $indicateur->unite }}</small>
                    </div>
                </div>
            </div>
        </div>

        <!-- Graphique -->
        <div class="row">
            <div class="col-lg-12">
                <div class="panel panel-default">
                    <div class="panel-heading">
                        <h3 class="panel-title">
                            <i class="fa fa-line-chart"></i> Évolution dans le Temps
                        </h3>
                        <div class="panel-actions">
                            <a href="{{ route('suivi-indicateurs.export', [$reforme->id, $indicateur->id]) }}" 
                               class="btn btn-info btn-xs">
                                <i class="fa fa-download"></i> Exporter CSV
                            </a>
                        </div>
                    </div>
                    <div class="panel-body">
                        <canvas id="graphiqueEvolution" width="400" height="100"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <!-- Historique des mesures -->
        <div class="row">
            <div class="col-lg-12">
                <div class="panel panel-default">
                    <div class="panel-heading">
                        <h3 class="panel-title">
                            <i class="fa fa-history"></i> Historique des Mesures
                        </h3>
                    </div>
                    <div class="panel-body">
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead>
                                    <tr>
                                        <th>Date</th>
                                        <th>Valeur</th>
                                        <th>Tendance</th>
                                        <th>Variation</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($reformeIndicateur->evolutions as $evolution)
                                        <tr>
                                            <td>
                                                <strong>{{ $evolution->date_formatee }}</strong>
                                            </td>
                                            <td>
                                                <span class="text-primary">
                                                    {{ number_format($evolution->valeur, 2, ',', ' ') }} {{ $indicateur->unite }}
                                                </span>
                                            </td>
                                            <td>
                                                {!! $evolution->icone_tendance !!}
                                                <span class="small">{{ $evolution->tendance }}</span>
                                            </td>
                                            <td>
                                                @if($evolution->pourcentage_variation != 0)
                                                    <span class="label {{ $evolution->pourcentage_variation > 0 ? 'label-success' : 'label-danger' }}">
                                                        {{ $evolution->pourcentage_variation > 0 ? '+' : '' }}{{ $evolution->pourcentage_variation }}%
                                                    </span>
                                                @else
                                                    <span class="text-muted">-</span>
                                                @endif
                                            </td>
                                            <td>
                                                <div class="btn-group btn-group-xs">
                                                    <a href="{{ route('suivi-indicateurs.edit', [$reforme->id, $indicateur->id, $evolution->date_evolution]) }}" 
                                                       class="btn btn-warning" title="Modifier">
                                                        <i class="fa fa-edit"></i>
                                                    </a>
                                                    <button type="button" 
                                                            class="btn btn-danger" 
                                                            title="Supprimer"
                                                            onclick="confirmerSuppression('{{ $evolution->date_evolution }}', '{{ $evolution->valeur }}')">
                                                        <i class="fa fa-trash"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    @endif
</div>

<!-- Modal de confirmation de suppression -->
<div class="modal fade" id="modalSuppression" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal">&times;</button>
                <h4 class="modal-title">Confirmer la suppression</h4>
            </div>
            <div class="modal-body">
                <p>Êtes-vous sûr de vouloir supprimer cette mesure ?</p>
                <div class="alert alert-warning">
                    <strong>Date :</strong> <span id="dateSuppression"></span><br>
                    <strong>Valeur :</strong> <span id="valeurSuppression"></span> {{ $indicateur->unite }}
                </div>
                <p class="text-danger">
                    <i class="fa fa-warning"></i> Cette action est irréversible.
                </p>
            </div>
            <div class="modal-footer">
                <form id="formSuppression" method="POST" style="display: inline;">
                    @csrf
                    @method('DELETE')
                    <button type="submit" class="btn btn-danger">
                        <i class="fa fa-trash"></i> Supprimer
                    </button>
                </form>
                <button type="button" class="btn btn-default" data-dismiss="modal">
                    <i class="fa fa-times"></i> Annuler
                </button>
            </div>
        </div>
    </div>
</div>

<style>
.metric-value {
    font-size: 28px;
    font-weight: bold;
    margin: 10px 0;
}

.panel-actions {
    float: right;
    margin-top: -5px;
}

.panel-actions .btn {
    margin-left: 5px;
}
</style>

<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    @if($donneesGraphique && count($donneesGraphique['data']) > 0)
        // Créer le graphique
        const ctx = document.getElementById('graphiqueEvolution').getContext('2d');
        const chart = new Chart(ctx, {
            type: 'line',
            data: {
                labels: {!! json_encode($donneesGraphique['labels']) !!},
                datasets: [{
                    label: '{{ $indicateur->libelle }} ({{ $indicateur->unite }})',
                    data: {!! json_encode($donneesGraphique['data']) !!},
                    borderColor: '#3498db',
                    backgroundColor: 'rgba(52, 152, 219, 0.1)',
                    borderWidth: 2,
                    fill: true,
                    tension: 0.4
                }]
            },
            options: {
                responsive: true,
                scales: {
                    y: {
                        beginAtZero: false,
                        title: {
                            display: true,
                            text: '{{ $indicateur->unite }}'
                        }
                    },
                    x: {
                        title: {
                            display: true,
                            text: 'Date'
                        }
                    }
                },
                plugins: {
                    legend: {
                        display: true,
                        position: 'top'
                    },
                    tooltip: {
                        mode: 'index',
                        intersect: false
                    }
                }
            }
        });
    @else
        // Afficher un message si pas de données
        document.getElementById('graphiqueEvolution').style.display = 'none';
        const container = document.getElementById('graphiqueEvolution').parentElement;
        container.innerHTML = '<div class="alert alert-info"><i class="fa fa-info-circle"></i> Aucune donnée à afficher dans le graphique.</div>';
    @endif
});

function confirmerSuppression(date, valeur) {
    document.getElementById('dateSuppression').textContent = date;
    document.getElementById('valeurSuppression').textContent = valeur;
    
    const form = document.getElementById('formSuppression');
    form.action = '{{ route("suivi-indicateurs.destroy", [$reforme->id, $indicateur->id, "DATE_PLACEHOLDER"]) }}'.replace('DATE_PLACEHOLDER', date);
    
    $('#modalSuppression').modal('show');
}
</script>
@endsection
