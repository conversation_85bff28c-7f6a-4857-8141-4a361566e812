<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Reforme;
use App\Models\Indicateur;
use App\Models\ReformeIndicateur;
use App\Models\EvolutionIndicateur;
use App\Helpers\NotificationHelper;
use Carbon\Carbon;

class SuiviIndicateurController extends Controller
{
    /**
     * Afficher la page générale de suivi des indicateurs
     */
    public function indexGeneral()
    {
        try {
            // Récupérer toutes les réformes avec leurs indicateurs
            $reformes = Reforme::with(['indicateurs', 'reformeIndicateurs.evolutions'])
                              ->orderBy('titre')
                              ->get();

            // Statistiques générales
            $totalIndicateurs = Indicateur::count();
            $totalEvolutions = EvolutionIndicateur::count();
            $indicateursActifs = ReformeIndicateur::distinct('indicateur_id')->count();

            // Évolutions récentes
            $evolutionsRecentes = EvolutionIndicateur::with(['reformeIndicateur.reforme', 'reformeIndicateur.indicateur'])
                                                    ->orderBy('date_evolution', 'desc')
                                                    ->limit(10)
                                                    ->get();

            return view('suivi-indicateurs.general', compact(
                'reformes',
                'totalIndicateurs',
                'totalEvolutions',
                'indicateursActifs',
                'evolutionsRecentes'
            ));

        } catch (\Exception $e) {
            return redirect()->back()->with('error', 'Erreur lors du chargement du suivi des indicateurs: ' . $e->getMessage());
        }
    }

    /**
     * Afficher la liste des indicateurs pour une réforme
     */
    public function index($reformeId)
    {
        try {
            $reforme = Reforme::with(['indicateurs', 'reformeIndicateurs.evolutions'])
                             ->findOrFail($reformeId);
            
            $indicateursAvecDonnees = $reforme->getIndicateursAvecValeurs();
            
            return view('suivi-indicateurs.index', compact('reforme', 'indicateursAvecDonnees'));
            
        } catch (\Exception $e) {
            return redirect()->back()->with('error', 'Erreur lors du chargement des indicateurs: ' . $e->getMessage());
        }
    }

    /**
     * Afficher le formulaire d'ajout d'une mesure
     */
    public function create($reformeId, $indicateurId)
    {
        try {
            $reforme = Reforme::findOrFail($reformeId);
            $indicateur = Indicateur::findOrFail($indicateurId);
            
            // Vérifier que l'indicateur est lié à cette réforme
            $reformeIndicateur = ReformeIndicateur::where('reforme_id', $reformeId)
                                                 ->where('indicateur_id', $indicateurId)
                                                 ->first();
            
            if (!$reformeIndicateur) {
                return redirect()->back()->with('error', 'Cet indicateur n\'est pas associé à cette réforme.');
            }
            
            // Obtenir la dernière valeur pour référence
            $derniereEvolution = $reformeIndicateur->getDerniereEvolution();
            
            return view('suivi-indicateurs.create', compact('reforme', 'indicateur', 'reformeIndicateur', 'derniereEvolution'));
            
        } catch (\Exception $e) {
            return redirect()->back()->with('error', 'Erreur lors du chargement du formulaire: ' . $e->getMessage());
        }
    }

    /**
     * Enregistrer une nouvelle mesure
     */
    public function store(Request $request, $reformeId, $indicateurId)
    {
        try {
            $request->validate([
                'date_evolution' => 'required|date|before_or_equal:today',
                'valeur' => 'required|numeric|min:0',
                'commentaire' => 'nullable|string|max:500'
            ], [
                'date_evolution.required' => 'La date de mesure est obligatoire.',
                'date_evolution.date' => 'La date doit être valide.',
                'date_evolution.before_or_equal' => 'La date ne peut pas être dans le futur.',
                'valeur.required' => 'La valeur est obligatoire.',
                'valeur.numeric' => 'La valeur doit être numérique.',
                'valeur.min' => 'La valeur ne peut pas être négative.'
            ]);

            $reforme = Reforme::findOrFail($reformeId);
            $indicateur = Indicateur::findOrFail($indicateurId);
            
            // Trouver ou créer la relation reforme-indicateur
            $reformeIndicateur = ReformeIndicateur::firstOrCreate([
                'reforme_id' => $reformeId,
                'indicateur_id' => $indicateurId
            ]);

            // Vérifier si une mesure existe déjà pour cette date
            $evolutionExistante = EvolutionIndicateur::where('reforme_indicateur_id', $reformeIndicateur->id)
                                                    ->where('date_evolution', $request->date_evolution)
                                                    ->first();

            if ($evolutionExistante) {
                return redirect()->back()
                                ->withInput()
                                ->with('error', 'Une mesure existe déjà pour cette date. Utilisez la fonction de modification.');
            }

            // Créer la nouvelle évolution
            $evolution = EvolutionIndicateur::create([
                'reforme_indicateur_id' => $reformeIndicateur->id,
                'date_evolution' => $request->date_evolution,
                'valeur' => $request->valeur
            ]);

            // Créer une notification
            NotificationHelper::info(
                "Nouvelle mesure ajoutée pour l'indicateur \"{$indicateur->libelle}\" de la réforme \"{$reforme->titre}\"",
                route('suivi-indicateurs.show', [$reformeId, $indicateurId]),
                auth()->id()
            );

            return redirect()->route('suivi-indicateurs.index', $reformeId)
                           ->with('success', 'Mesure ajoutée avec succès.');

        } catch (\Exception $e) {
            return redirect()->back()
                           ->withInput()
                           ->with('error', 'Erreur lors de l\'enregistrement: ' . $e->getMessage());
        }
    }

    /**
     * Afficher le détail d'un indicateur avec son historique
     */
    public function show($reformeId, $indicateurId)
    {
        try {
            $reforme = Reforme::findOrFail($reformeId);
            $indicateur = Indicateur::findOrFail($indicateurId);
            
            $reformeIndicateur = ReformeIndicateur::where('reforme_id', $reformeId)
                                                 ->where('indicateur_id', $indicateurId)
                                                 ->with(['evolutions' => function($query) {
                                                     $query->orderBy('date_evolution', 'desc');
                                                 }])
                                                 ->first();
            
            if (!$reformeIndicateur) {
                return redirect()->back()->with('error', 'Cet indicateur n\'est pas associé à cette réforme.');
            }

            $statistiques = $reformeIndicateur->getStatistiquesCompletes();
            $donneesGraphique = $reformeIndicateur->getDonneesGraphique();
            
            return view('suivi-indicateurs.show', compact(
                'reforme', 
                'indicateur', 
                'reformeIndicateur', 
                'statistiques', 
                'donneesGraphique'
            ));
            
        } catch (\Exception $e) {
            return redirect()->back()->with('error', 'Erreur lors du chargement des détails: ' . $e->getMessage());
        }
    }

    /**
     * Afficher le formulaire de modification d'une mesure
     */
    public function edit($reformeId, $indicateurId, $date)
    {
        try {
            $reforme = Reforme::findOrFail($reformeId);
            $indicateur = Indicateur::findOrFail($indicateurId);
            
            $reformeIndicateur = ReformeIndicateur::where('reforme_id', $reformeId)
                                                 ->where('indicateur_id', $indicateurId)
                                                 ->firstOrFail();
            
            $evolution = EvolutionIndicateur::where('reforme_indicateur_id', $reformeIndicateur->id)
                                          ->where('date_evolution', $date)
                                          ->firstOrFail();
            
            return view('suivi-indicateurs.edit', compact('reforme', 'indicateur', 'reformeIndicateur', 'evolution'));
            
        } catch (\Exception $e) {
            return redirect()->back()->with('error', 'Erreur lors du chargement de la mesure: ' . $e->getMessage());
        }
    }

    /**
     * Mettre à jour une mesure existante
     */
    public function update(Request $request, $reformeId, $indicateurId, $date)
    {
        try {
            $request->validate([
                'valeur' => 'required|numeric|min:0',
                'commentaire' => 'nullable|string|max:500'
            ], [
                'valeur.required' => 'La valeur est obligatoire.',
                'valeur.numeric' => 'La valeur doit être numérique.',
                'valeur.min' => 'La valeur ne peut pas être négative.'
            ]);

            $reforme = Reforme::findOrFail($reformeId);
            $indicateur = Indicateur::findOrFail($indicateurId);
            
            $reformeIndicateur = ReformeIndicateur::where('reforme_id', $reformeId)
                                                 ->where('indicateur_id', $indicateurId)
                                                 ->firstOrFail();
            
            $evolution = EvolutionIndicateur::where('reforme_indicateur_id', $reformeIndicateur->id)
                                          ->where('date_evolution', $date)
                                          ->firstOrFail();

            $ancienneValeur = $evolution->valeur;
            $evolution->update(['valeur' => $request->valeur]);

            // Créer une notification
            NotificationHelper::warning(
                "Mesure modifiée pour l'indicateur \"{$indicateur->libelle}\" (de {$ancienneValeur} à {$request->valeur})",
                route('suivi-indicateurs.show', [$reformeId, $indicateurId]),
                auth()->id()
            );

            return redirect()->route('suivi-indicateurs.show', [$reformeId, $indicateurId])
                           ->with('success', 'Mesure mise à jour avec succès.');

        } catch (\Exception $e) {
            return redirect()->back()
                           ->withInput()
                           ->with('error', 'Erreur lors de la mise à jour: ' . $e->getMessage());
        }
    }

    /**
     * Supprimer une mesure
     */
    public function destroy($reformeId, $indicateurId, $date)
    {
        try {
            $reforme = Reforme::findOrFail($reformeId);
            $indicateur = Indicateur::findOrFail($indicateurId);
            
            $reformeIndicateur = ReformeIndicateur::where('reforme_id', $reformeId)
                                                 ->where('indicateur_id', $indicateurId)
                                                 ->firstOrFail();
            
            $evolution = EvolutionIndicateur::where('reforme_indicateur_id', $reformeIndicateur->id)
                                          ->where('date_evolution', $date)
                                          ->firstOrFail();

            $valeurSupprimee = $evolution->valeur;
            $evolution->delete();

            // Créer une notification
            NotificationHelper::error(
                "Mesure supprimée pour l'indicateur \"{$indicateur->libelle}\" (valeur: {$valeurSupprimee})",
                route('suivi-indicateurs.show', [$reformeId, $indicateurId]),
                auth()->id()
            );

            return redirect()->route('suivi-indicateurs.show', [$reformeId, $indicateurId])
                           ->with('success', 'Mesure supprimée avec succès.');

        } catch (\Exception $e) {
            return redirect()->back()->with('error', 'Erreur lors de la suppression: ' . $e->getMessage());
        }
    }

    /**
     * Obtenir les données pour un graphique (AJAX)
     */
    public function getDonneesGraphique($reformeId, $indicateurId)
    {
        try {
            $reformeIndicateur = ReformeIndicateur::where('reforme_id', $reformeId)
                                                 ->where('indicateur_id', $indicateurId)
                                                 ->firstOrFail();
            
            $donnees = $reformeIndicateur->getDonneesGraphique();
            
            return response()->json([
                'success' => true,
                'donnees' => $donnees
            ]);
            
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Erreur lors du chargement des données: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Obtenir les statistiques d'un indicateur (AJAX)
     */
    public function getStatistiques($reformeId, $indicateurId)
    {
        try {
            $reformeIndicateur = ReformeIndicateur::where('reforme_id', $reformeId)
                                                 ->where('indicateur_id', $indicateurId)
                                                 ->firstOrFail();
            
            $statistiques = $reformeIndicateur->getStatistiquesCompletes();
            
            return response()->json([
                'success' => true,
                'statistiques' => $statistiques
            ]);
            
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Erreur lors du chargement des statistiques: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Exporter les données d'un indicateur en CSV
     */
    public function exporterCSV($reformeId, $indicateurId)
    {
        try {
            $reforme = Reforme::findOrFail($reformeId);
            $indicateur = Indicateur::findOrFail($indicateurId);
            
            $reformeIndicateur = ReformeIndicateur::where('reforme_id', $reformeId)
                                                 ->where('indicateur_id', $indicateurId)
                                                 ->with(['evolutions' => function($query) {
                                                     $query->orderBy('date_evolution');
                                                 }])
                                                 ->firstOrFail();

            $filename = "indicateur_{$indicateur->libelle}_{$reforme->titre}_" . date('Y-m-d') . ".csv";
            
            $headers = [
                'Content-Type' => 'text/csv',
                'Content-Disposition' => "attachment; filename=\"{$filename}\"",
            ];

            $callback = function() use ($reformeIndicateur, $indicateur) {
                $file = fopen('php://output', 'w');
                
                // En-têtes CSV
                fputcsv($file, ['Date', 'Valeur', 'Unité', 'Tendance', 'Variation %']);
                
                // Données
                foreach ($reformeIndicateur->evolutions as $evolution) {
                    fputcsv($file, [
                        $evolution->date_formatee,
                        $evolution->valeur,
                        $indicateur->unite,
                        $evolution->tendance,
                        $evolution->pourcentage_variation
                    ]);
                }
                
                fclose($file);
            };

            return response()->stream($callback, 200, $headers);
            
        } catch (\Exception $e) {
            return redirect()->back()->with('error', 'Erreur lors de l\'export: ' . $e->getMessage());
        }
    }
}
